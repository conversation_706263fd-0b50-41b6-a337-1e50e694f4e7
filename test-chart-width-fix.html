<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Width Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        
        .chart-container {
            width: 100%;
            height: 320px;
            background: #333;
            border-radius: 8px;
            margin: 20px 0;
            position: relative;
        }
        
        h2 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .description {
            color: #ccc;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            background: #4CAF50;
            color: white;
        }
        
        .error {
            background: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chart Width Fix Test</h1>
        <p class="description">Testing the fix for Last Week's Sales chart to use full width like Today vs Previous Years chart.</p>
        
        <h2>Test 1: Last Week's Sales Chart (Fixed)</h2>
        <p class="description">Should now use full width with scrollable-stacked-column type and fullWidthDistribution options.</p>
        <div id="last-week-chart" class="chart-container"></div>
        <div id="last-week-status" class="status"></div>
        
        <h2>Test 2: Today vs Previous Years Chart (Reference)</h2>
        <p class="description">Reference chart that already works correctly with full width.</p>
        <div id="today-vs-chart" class="chart-container"></div>
        <div id="today-vs-status" class="status"></div>
    </div>

    <!-- Load Chart JavaScript -->
    <script src="components/charts/snap-charts.js"></script>
    
    <script>
        // Test data for Last Week's Sales
        const lastWeekData = [
            { date: 'Jul 21', sales: 142, values: [45, 32, 28, 37], royalties: 78 },
            { date: 'Jul 22', sales: 98, values: [28, 25, 20, 25], royalties: 54 },
            { date: 'Jul 23', sales: 186, values: [52, 48, 41, 45], royalties: 102 },
            { date: 'Jul 24', sales: 94, values: [25, 22, 24, 23], royalties: 51 },
            { date: 'Jul 25', sales: 165, values: [48, 42, 38, 37], royalties: 89 },
            { date: 'Jul 26', sales: 203, values: [58, 52, 48, 45], royalties: 112 },
            { date: 'Jul 27', sales: 261, values: [72, 68, 62, 59], royalties: 143 }
        ];

        // Test data for Today vs Previous Years
        const todayVsData = [
            { date: 'Jul 28', year: '2000', sales: 489, values: [142, 128, 115, 104], royalties: 267, isCurrentYear: false },
            { date: 'Jul 28', year: '2001', sales: 328, values: [98, 85, 78, 67], royalties: 179, isCurrentYear: false },
            { date: 'Jul 28', year: '2002', sales: 197, values: [58, 52, 45, 42], royalties: 108, isCurrentYear: false },
            { date: 'Jul 28', year: '2003', sales: 366, values: [108, 95, 88, 75], royalties: 200, isCurrentYear: false },
            { date: 'Jul 28', year: '2004', sales: 385, values: [115, 102, 92, 76], royalties: 210, isCurrentYear: false },
            { date: 'Jul 28', year: '2005', sales: 237, values: [68, 62, 55, 52], royalties: 129, isCurrentYear: false },
            { date: 'Jul 28', year: '2006', sales: 216, values: [62, 58, 48, 48], royalties: 118, isCurrentYear: false },
            { date: 'Jul 28', year: '2007', sales: 274, values: [78, 72, 65, 59], royalties: 150, isCurrentYear: false },
            { date: 'Jul 28', year: '2008', sales: 271, values: [82, 68, 62, 59], royalties: 148, isCurrentYear: false },
            { date: 'Jul 28', year: '2009', sales: 351, values: [105, 92, 82, 72], royalties: 192, isCurrentYear: false },
            { date: 'Jul 28', year: '2010', sales: 273, values: [82, 72, 65, 54], royalties: 149, isCurrentYear: false },
            { date: 'Jul 28', year: '2011', sales: 555, values: [168, 145, 128, 114], royalties: 303, isCurrentYear: false },
            { date: 'Jul 28', year: '2012', sales: 462, values: [138, 122, 105, 97], royalties: 252, isCurrentYear: false },
            { date: 'Jul 28', year: '2013', sales: 526, values: [158, 138, 122, 108], royalties: 287, isCurrentYear: false },
            { date: 'Jul 28', year: '2014', sales: 844, values: [252, 222, 195, 175], royalties: 461, isCurrentYear: false },
            { date: 'Jul 28', year: '2015', sales: 359, values: [108, 95, 82, 74], royalties: 196, isCurrentYear: false },
            { date: 'Jul 28', year: '2016', sales: 557, values: [168, 145, 128, 116], royalties: 304, isCurrentYear: false },
            { date: 'Jul 28', year: '2017', sales: 489, values: [148, 128, 112, 101], royalties: 267, isCurrentYear: false },
            { date: 'Jul 28', year: '2018', sales: 153, values: [45, 42, 35, 31], royalties: 83, isCurrentYear: false },
            { date: 'Jul 28', year: '2019', sales: 511, values: [155, 135, 118, 103], royalties: 279, isCurrentYear: false }
        ];

        try {
            // Test 1: Last Week's Sales Chart with fixed configuration
            const lastWeekChart = new SnapChart({
                container: '#last-week-chart',
                type: 'scrollable-stacked-column',
                data: lastWeekData,
                demoOptions: {
                    showContainer: false,
                    showTitle: false,
                    showDataEditor: false,
                    showControls: false,
                    showInsights: false
                },
                options: {
                    responsive: true,
                    animate: true,
                    height: 300,
                    compareMode: false,
                    isTodayVsPreviousYearsChart: true, // Required for full-width distribution logic
                    fullWidthDistribution: true // Enable full-width column distribution
                }
            });
            
            document.getElementById('last-week-status').className = 'status success';
            document.getElementById('last-week-status').textContent = '✅ Last Week\'s Sales Chart initialized successfully with full-width distribution';
            
        } catch (error) {
            document.getElementById('last-week-status').className = 'status error';
            document.getElementById('last-week-status').textContent = '❌ Error initializing Last Week\'s Sales Chart: ' + error.message;
            console.error('Last Week Chart Error:', error);
        }

        try {
            // Test 2: Today vs Previous Years Chart (reference)
            const todayVsChart = new SnapChart({
                container: '#today-vs-chart',
                type: 'scrollable-stacked-column',
                data: todayVsData,
                demoOptions: {
                    showContainer: false,
                    showTitle: false,
                    showDataEditor: false,
                    showControls: false,
                    showInsights: false
                },
                options: {
                    responsive: true,
                    animate: true,
                    height: 300,
                    isTodayVsPreviousYearsChart: true, // Required for full-width distribution logic
                    fullWidthDistribution: true // Enable full-width column distribution
                }
            });
            
            document.getElementById('today-vs-status').className = 'status success';
            document.getElementById('today-vs-status').textContent = '✅ Today vs Previous Years Chart initialized successfully (reference)';
            
        } catch (error) {
            document.getElementById('today-vs-status').className = 'status error';
            document.getElementById('today-vs-status').textContent = '❌ Error initializing Today vs Previous Years Chart: ' + error.message;
            console.error('Today vs Chart Error:', error);
        }
    </script>
</body>
</html>
